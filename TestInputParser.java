import java.io.FileNotFoundException;

/**
 * TestInputParser.java - Test class for the InputParser utility
 * This class validates the InputParser functionality with various test cases
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TestInputParser {
    
    public static void main(String[] args) {
        System.out.println("=== Testing InputParser Utility ===\n");
        
        // Test with datafile1.txt
        testParseFile("datafile1.txt");
        
        System.out.println("\n==================================================\n");

        // Test with datafile2.txt
        testParseFile("datafile2.txt");

        System.out.println("\n==================================================\n");

        // Test error handling
        testErrorHandling();

        System.out.println("\n==================================================\n");
        
        // Test integration with scheduling algorithms
        testIntegrationWithScheduling();
    }
    
    /**
     * Tests parsing a specific file
     */
    private static void testParseFile(String fileName) {
        System.out.println("Testing file: " + fileName);
        
        try {
            InputData inputData = InputParser.parseInputFile(fileName);
            
            System.out.println("✓ File parsed successfully");
            System.out.println("Dispatcher Time: " + inputData.getDispatcherTime());
            System.out.println("Number of Tasks: " + inputData.getTaskCount());
            System.out.println("Valid: " + inputData.isValid());
            
            System.out.println("\nTasks:");
            for (Task task : inputData.getTasks()) {
                System.out.printf("  %s: ArrTime=%d, SrvTime=%d%n", 
                    task.getPID(), task.getArrTime(), task.getSrvTime());
            }
            
            System.out.println("\nDetailed representation:");
            System.out.println(inputData.toDetailedString());
            
        } catch (FileNotFoundException e) {
            System.err.println("✗ File not found: " + e.getMessage());
        } catch (InputParser.InputParseException e) {
            System.err.println("✗ Parse error: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ Unexpected error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Tests error handling with invalid inputs
     */
    private static void testErrorHandling() {
        System.out.println("Testing Error Handling:");
        
        // Test with non-existent file
        System.out.println("\n1. Testing non-existent file:");
        try {
            InputParser.parseInputFile("nonexistent.txt");
            System.err.println("✗ Should have thrown FileNotFoundException");
        } catch (FileNotFoundException e) {
            System.out.println("✓ Correctly caught FileNotFoundException: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("✗ Unexpected exception: " + e.getMessage());
        }
        
        // Test with malformed content (create a temporary test)
        System.out.println("\n2. Testing validation:");
        try {
            // Test that we can create valid InputData
            java.util.List<Task> tasks = new java.util.ArrayList<>();
            tasks.add(new Task("p1", 0, 5));
            InputData validData = new InputData(1, tasks);
            System.out.println("✓ Valid InputData created: " + validData.isValid());
            
            // Test empty task list
            java.util.List<Task> emptyTasks = new java.util.ArrayList<>();
            InputData invalidData = new InputData(1, emptyTasks);
            System.out.println("✓ Empty task list validation: " + !invalidData.isValid());
            
        } catch (Exception e) {
            System.err.println("✗ Validation test failed: " + e.getMessage());
        }
    }
    
    /**
     * Tests integration with scheduling algorithms
     */
    private static void testIntegrationWithScheduling() {
        System.out.println("Testing Integration with Scheduling Algorithms:");
        
        try {
            // Parse input file
            InputData inputData = InputParser.parseInputFile("datafile1.txt");
            System.out.println("✓ Input parsed successfully");
            
            // Test with FCFS algorithm
            FCFSScheduling fcfs = new FCFSScheduling(inputData.getDispatcherTime(), inputData.getTasks());
            fcfs.run();
            
            System.out.println("✓ FCFS algorithm executed successfully");
            System.out.println("Average Turnaround Time: " + 
                String.format("%.2f", fcfs.calculateAverageTurnaroundTime()));
            System.out.println("Average Waiting Time: " + 
                String.format("%.2f", fcfs.calculateAverageWaitingTime()));
            
            // Test with Round Robin algorithm
            RoundRobinScheduling rr = new RoundRobinScheduling(
                inputData.getDispatcherTime(), inputData.getTasks(), 4);
            rr.run();
            
            System.out.println("✓ Round Robin algorithm executed successfully");
            System.out.println("RR Average Turnaround Time: " + 
                String.format("%.2f", rr.calculateAverageTurnaroundTime()));
            System.out.println("RR Average Waiting Time: " + 
                String.format("%.2f", rr.calculateAverageWaitingTime()));
            
        } catch (Exception e) {
            System.err.println("✗ Integration test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Helper method to create separator (Java 8 compatible)
     */
    private static String repeat(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
