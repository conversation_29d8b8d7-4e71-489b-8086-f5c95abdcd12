/**
 * Task.java - Represents a process in the scheduling simulation
 * This class implements Comparable<Task> interface and contains process information
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class NewTask implements Comparable<NewTask> {
    
    // Private member variables as specified
    private String PID;        // Process ID
    private int Arrt;          // Arrival time
    private int SrvTime;       // Service time
    private int workAT;        // Work arrival time
    private int workSt;        // Work start time
    
    /**
     * Default constructor
     */
    public NewTask() {
        this.PID = "";
        this.Arrt = 0;
        this.SrvTime = 0;
        this.workAT = 0;
        this.workSt = 0;
    }
    
    /**
     * Public constructor with basic process information
     * Initializes workAT to arrt and workSt to srvTime as suggested for reinitialisation
     *
     * @param pid Process ID
     * @param arrt Arrival time
     * @param srvTime Service time
     */
    public NewTask(String pid, int arrt, int srvTime) {
        this.PID = pid;
        this.Arrt = arrt;
        this.SrvTime = srvTime;
        this.workAT = arrt;      // Initialize workAT to arrt
        this.workSt = srvTime;   // Initialize workSt to srvTime
    }
    
    /**
     * Constructor with all parameters
     * 
     * @param PID Process ID
     * @param Arrt Arrival time
     * @param SrvTime Service time
     * @param workAT Work arrival time
     * @param workSt Work start time
     */
    public NewTask(String PID, int Arrt, int SrvTime, int workAT, int workSt) {
        this.PID = PID;
        this.Arrt = Arrt;
        this.SrvTime = SrvTime;
        this.workAT = workAT;
        this.workSt = workSt;
    }
    
    // Getter methods
    public String getPID() {
        return PID;
    }
    
    public int getArrt() {
        return Arrt;
    }
    
    public int getSrvTime() {
        return SrvTime;
    }
    
    public int getWorkAT() {
        return workAT;
    }
    
    public int getWorkSt() {
        return workSt;
    }
    
    // Setter methods
    public void setPID(String PID) {
        this.PID = PID;
    }
    
    public void setArrt(int Arrt) {
        this.Arrt = Arrt;
    }
    
    public void setSrvTime(int SrvTime) {
        this.SrvTime = SrvTime;
    }
    
    /**
     * Sets the work arrival time
     *
     * @param time The work arrival time to set
     */
    public void setWorkAT(int time) {
        this.workAT = time;
    }

    /**
     * Sets the work start time
     *
     * @param time The work start time to set
     */
    public void setWorkSt(int time) {
        this.workSt = time;
    }
    
    /**
     * Compares this task with another task for ordering
     * Primary sort: by arrival time (Arrt)
     * Secondary sort: by PID (numerically, e.g., p1 before p2)
     * 
     * @param other The task to compare with
     * @return negative if this task should come first, positive if other should come first, 0 if equal
     */
    @Override
    public int compareTo(NewTask other) {
        // Primary comparison: by arrival time
        int arrTimeComparison = Integer.compare(this.Arrt, other.Arrt);
        if (arrTimeComparison != 0) {
            return arrTimeComparison;
        }
        
        // Secondary comparison: by PID (numerically)
        return comparePIDs(this.PID, other.PID);
    }
    
    /**
     * Helper method to compare PIDs numerically
     * Assumes PID format like "p1", "p2", etc.
     * 
     * @param pid1 First PID to compare
     * @param pid2 Second PID to compare
     * @return comparison result
     */
    private int comparePIDs(String pid1, String pid2) {
        try {
            // Extract numeric part (assuming format like "p1", "p2")
            int num1 = Integer.parseInt(pid1.substring(1));
            int num2 = Integer.parseInt(pid2.substring(1));
            return Integer.compare(num1, num2);
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            // Fallback to string comparison if numeric extraction fails
            return pid1.compareTo(pid2);
        }
    }
    
    /**
     * String representation of the task
     * 
     * @return String representation of the task
     */
    @Override
    public String toString() {
        return String.format("Task{PID='%s', Arrt=%d, SrvTime=%d, workAT=%d, workSt=%d}",
                           PID, Arrt, SrvTime, workAT, workSt);
    }
    
    /**
     * Equality check based on PID
     * 
     * @param obj Object to compare with
     * @return true if the objects represent the same task (same PID)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        NewTask task = (NewTask) obj;
        return PID != null ? PID.equals(task.PID) : task.PID == null;
    }
    
    /**
     * Hash code based on PID
     * 
     * @return hash code for the task
     */
    @Override
    public int hashCode() {
        return PID != null ? PID.hashCode() : 0;
    }
    
    /**
     * Creates a copy of this task
     * 
     * @return A new Task object with the same values
     */
    public NewTask copy() {
        return new NewTask(this.PID, this.Arrt, this.SrvTime, this.workAT, this.workSt);
    }
    
    /**
     * Resets work-related fields to default values
     */
    public void resetWork() {
        this.workAT = 0;
        this.workSt = 0;
    }
}
