import java.util.List;
import java.util.ArrayList;
import java.text.DecimalFormat;

/**
 * SchedulingAlgorithm.java - Abstract base class for all scheduling algorithms
 * This class provides the common structure and functionality for scheduling simulations
 * 
 * <AUTHOR>
 * @version 1.0
 */
public abstract class SchedulingAlgorithm {
    
    // Core simulation data
    protected int dispatcherTime;           // Time overhead for context switching
    protected List<Task> tasks;             // List of tasks to be scheduled
    protected List<Task> originalTasks;     // Original task list for reset purposes
    
    // Simulation results tracking
    protected List<ExecutionEvent> executionOrder;  // Record of process execution events
    protected boolean simulationCompleted;          // Flag to track if simulation has run
    
    /**
     * Inner class to represent an execution event
     * Records when a process starts execution
     */
    protected static class ExecutionEvent {
        private final int time;
        private final String processId;
        
        public ExecutionEvent(int time, String processId) {
            this.time = time;
            this.processId = processId;
        }
        
        public int getTime() { return time; }
        public String getProcessId() { return processId; }
        
        @Override
        public String toString() {
            return "T" + time + ": " + processId;
        }
    }
    
    /**
     * Constructor for SchedulingAlgorithm
     * 
     * @param dispatcherTime Time overhead for context switching
     * @param tasks List of tasks to be scheduled
     */
    public SchedulingAlgorithm(int dispatcherTime, List<Task> tasks) {
        this.dispatcherTime = dispatcherTime;
        this.tasks = new ArrayList<>();
        this.originalTasks = new ArrayList<>();
        
        // Create deep copies of tasks to avoid modifying originals
        for (Task task : tasks) {
            Task taskCopy = new Task(task.getPID(), task.getArrTime(), task.getSrvTime());
            this.tasks.add(taskCopy);
            
            Task originalCopy = new Task(task.getPID(), task.getArrTime(), task.getSrvTime());
            this.originalTasks.add(originalCopy);
        }
        
        this.executionOrder = new ArrayList<>();
        this.simulationCompleted = false;
    }
    
    /**
     * Abstract method to run the specific scheduling algorithm
     * Must be implemented by each concrete scheduling algorithm
     */
    public abstract void run();
    
    /**
     * Abstract method to output detailed results
     * Must follow the format: "T<time>: <processId>" for each execution event
     * Followed by a table showing Process, Turnaround Time, and Waiting Time
     */
    public abstract void outResults();
    
    /**
     * Abstract method to output summary statistics
     * Must follow the format showing algorithm name, average turnaround time, and average waiting time
     */
    public abstract void outSummary();
    
    /**
     * Records an execution event when a process starts running
     * 
     * @param time The time when the process starts execution
     * @param processId The ID of the process being executed
     */
    protected void recordExecution(int time, String processId) {
        executionOrder.add(new ExecutionEvent(time, processId));
    }
    
    /**
     * Calculates turnaround time for a task
     * TurnaroundTime = FinishTime - ArrivalTime
     * 
     * @param task The task to calculate turnaround time for
     * @return The turnaround time
     */
    protected long calculateTurnaroundTime(Task task) {
        return task.getFinishTime() - task.getArrTime();
    }
    
    /**
     * Calculates waiting time for a task
     * WaitingTime = TurnaroundTime - ServiceTime
     * 
     * @param task The task to calculate waiting time for
     * @return The waiting time
     */
    protected long calculateWaitingTime(Task task) {
        long turnaroundTime = calculateTurnaroundTime(task);
        return turnaroundTime - task.getSrvTime();
    }
    
    /**
     * Calculates average turnaround time across all tasks
     * 
     * @return The average turnaround time
     */
    protected double calculateAverageTurnaroundTime() {
        if (tasks.isEmpty()) return 0.0;
        
        long totalTurnaroundTime = 0;
        for (Task task : tasks) {
            totalTurnaroundTime += calculateTurnaroundTime(task);
        }
        
        return (double) totalTurnaroundTime / tasks.size();
    }
    
    /**
     * Calculates average waiting time across all tasks
     * 
     * @return The average waiting time
     */
    protected double calculateAverageWaitingTime() {
        if (tasks.isEmpty()) return 0.0;
        
        long totalWaitingTime = 0;
        for (Task task : tasks) {
            totalWaitingTime += calculateWaitingTime(task);
        }
        
        return (double) totalWaitingTime / tasks.size();
    }
    
    /**
     * Updates the turnaround and waiting times for all tasks
     * Should be called after simulation completion
     */
    protected void updateTaskTimes() {
        for (Task task : tasks) {
            long turnaroundTime = calculateTurnaroundTime(task);
            long waitingTime = calculateWaitingTime(task);
            
            task.setTurnaroundTime(turnaroundTime);
            task.setWaitingTime(waitingTime);
        }
    }
    
    /**
     * Resets all tasks to their original state for a new simulation
     */
    protected void resetTasks() {
        for (Task task : tasks) {
            task.reset();
        }
        executionOrder.clear();
        simulationCompleted = false;
    }
    
    /**
     * Gets the list of execution events
     * 
     * @return List of execution events in chronological order
     */
    protected List<ExecutionEvent> getExecutionOrder() {
        return new ArrayList<>(executionOrder);
    }
    
    /**
     * Gets the dispatcher time
     * 
     * @return The dispatcher time
     */
    public int getDispatcherTime() {
        return dispatcherTime;
    }
    
    /**
     * Gets a copy of the tasks list
     * 
     * @return Copy of the tasks list
     */
    public List<Task> getTasks() {
        return new ArrayList<>(tasks);
    }
    
    /**
     * Checks if the simulation has been completed
     * 
     * @return true if simulation has been run, false otherwise
     */
    public boolean isSimulationCompleted() {
        return simulationCompleted;
    }
    
    /**
     * Formats a double value to 2 decimal places for output
     * 
     * @param value The value to format
     * @return Formatted string
     */
    protected String formatDecimal(double value) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }
}
