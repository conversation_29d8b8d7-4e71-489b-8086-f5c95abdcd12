/**
 * Task.java - Represents a process in the scheduling simulation
 * This class encapsulates all properties and behaviors of a process/task
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Task implements Comparable<Task> {
    
    // Core process properties
    private String PID;              // Process ID (e.g., "p1")
    private long ArrTime;            // Arrival time
    private long SrvTime;            // Service time (original)
    private long WorkRemaining;      // Remaining service time
    private long FinishTime;         // When the process completes
    private long WaitingTime;        // Total waiting time
    private long TurnaroundTime;     // Total turnaround time
    private long LastRunTime;        // Last time the process was run
    private long CurrentQuantum;     // Time quantum for SRR and FB
    
    // Store original values for reset functionality
    private final long originalSrvTime;
    
    /**
     * Constructor to create a new Task
     * 
     * @param PID Process ID
     * @param ArrTime Arrival time
     * @param SrvTime Service time
     */
    public Task(String PID, long ArrTime, long SrvTime) {
        this.PID = PID;
        this.ArrTime = ArrTime;
        this.SrvTime = SrvTime;
        this.originalSrvTime = SrvTime; // Store original for reset
        this.WorkRemaining = SrvTime;   // Initially equals service time
        
        // Initialize other properties to default values
        this.FinishTime = 0;
        this.WaitingTime = 0;
        this.TurnaroundTime = 0;
        this.LastRunTime = 0;
        this.CurrentQuantum = 0;
    }
    
    /**
     * Resets the task to its initial state for a new simulation run
     * Resets WorkRemaining, FinishTime, WaitingTime, TurnaroundTime, LastRunTime, and CurrentQuantum
     */
    public void reset() {
        this.WorkRemaining = this.originalSrvTime;
        this.FinishTime = 0;
        this.WaitingTime = 0;
        this.TurnaroundTime = 0;
        this.LastRunTime = 0;
        this.CurrentQuantum = 0;
    }
    
    /**
     * Simulates the execution of the task for a given time slice
     * 
     * @param timeSlice The amount of time to execute the task
     * @return The actual time executed (may be less than timeSlice if task completes)
     */
    public long execute(long timeSlice) {
        long timeExecuted = Math.min(timeSlice, WorkRemaining);
        WorkRemaining -= timeExecuted;
        return timeExecuted;
    }
    
    /**
     * Checks if the task is completed (no work remaining)
     * 
     * @return true if the task is completed, false otherwise
     */
    public boolean isCompleted() {
        return WorkRemaining <= 0;
    }
    
    // Getter methods
    public String getPID() { return PID; }
    public long getArrTime() { return ArrTime; }
    public long getSrvTime() { return SrvTime; }
    public long getWorkRemaining() { return WorkRemaining; }
    public long getFinishTime() { return FinishTime; }
    public long getWaitingTime() { return WaitingTime; }
    public long getTurnaroundTime() { return TurnaroundTime; }
    public long getLastRunTime() { return LastRunTime; }
    public long getCurrentQuantum() { return CurrentQuantum; }
    
    // Setter methods for mutable properties
    public void setWorkRemaining(long workRemaining) { this.WorkRemaining = workRemaining; }
    public void setFinishTime(long finishTime) { this.FinishTime = finishTime; }
    public void setWaitingTime(long waitingTime) { this.WaitingTime = waitingTime; }
    public void setTurnaroundTime(long turnaroundTime) { this.TurnaroundTime = turnaroundTime; }
    public void setLastRunTime(long lastRunTime) { this.LastRunTime = lastRunTime; }
    public void setCurrentQuantum(long currentQuantum) { this.CurrentQuantum = currentQuantum; }
    
    /**
     * Compares this task with another task for ordering
     * Primary sort: by ArrTime (arrival time)
     * Secondary sort: by PID (numerically, e.g., p1 before p2)
     * 
     * @param other The task to compare with
     * @return negative if this task should come first, positive if other should come first, 0 if equal
     */
    @Override
    public int compareTo(Task other) {
        // Primary comparison: by arrival time
        int arrTimeComparison = Long.compare(this.ArrTime, other.ArrTime);
        if (arrTimeComparison != 0) {
            return arrTimeComparison;
        }
        
        // Secondary comparison: by PID (numerically)
        // Extract numeric part from PID (assuming format like "p1", "p2", etc.)
        return comparePIDs(this.PID, other.PID);
    }
    
    /**
     * Helper method to compare PIDs numerically
     * Assumes PID format like "p1", "p2", etc.
     * 
     * @param pid1 First PID to compare
     * @param pid2 Second PID to compare
     * @return comparison result
     */
    private int comparePIDs(String pid1, String pid2) {
        try {
            // Extract numeric part (assuming format like "p1", "p2")
            int num1 = Integer.parseInt(pid1.substring(1));
            int num2 = Integer.parseInt(pid2.substring(1));
            return Integer.compare(num1, num2);
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            // Fallback to string comparison if numeric extraction fails
            return pid1.compareTo(pid2);
        }
    }
    
    /**
     * String representation of the task for debugging and output
     * 
     * @return String representation of the task
     */
    @Override
    public String toString() {
        return String.format("Task{PID='%s', ArrTime=%d, SrvTime=%d, WorkRemaining=%d, " +
                           "FinishTime=%d, WaitingTime=%d, TurnaroundTime=%d}",
                           PID, ArrTime, SrvTime, WorkRemaining, FinishTime, WaitingTime, TurnaroundTime);
    }
    
    /**
     * Equality check based on PID
     * 
     * @param obj Object to compare with
     * @return true if the objects represent the same task (same PID)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Task task = (Task) obj;
        return PID.equals(task.PID);
    }
    
    /**
     * Hash code based on PID
     * 
     * @return hash code for the task
     */
    @Override
    public int hashCode() {
        return PID.hashCode();
    }
}
