import java.util.LinkedList;
import java.util.Queue;
import java.util.ArrayList;
import java.util.List;

/**
 * RoundRobinScheduling.java - Round Robin scheduling algorithm implementation
 * This class implements the Round Robin scheduling algorithm using the SchedulingAlgorithm framework
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RoundRobinScheduling extends SchedulingAlgorithm {
    
    private int timeQuantum;  // Time quantum for round robin
    
    /**
     * Constructor for Round Robin scheduling algorithm
     * 
     * @param dispatcherTime Time overhead for context switching
     * @param tasks List of tasks to be scheduled
     * @param timeQuantum Time quantum for round robin scheduling
     */
    public RoundRobinScheduling(int dispatcherTime, List<Task> tasks, int timeQuantum) {
        super(dispatcherTime, tasks);
        this.timeQuantum = timeQuantum;
    }
    
    /**
     * Runs the Round Robin scheduling algorithm
     * Uses a circular queue to manage process execution with time quantum
     */
    @Override
    public void run() {
        // Reset tasks and execution order for fresh simulation
        resetTasks();
        
        Queue<Task> readyQueue = new LinkedList<>();
        List<Task> arrivedTasks = new ArrayList<>();
        int currentTime = 0;
        
        // Sort tasks by arrival time for processing
        java.util.Collections.sort(tasks);
        
        // Add initially arrived tasks to ready queue
        for (Task task : tasks) {
            if (task.getArrTime() <= currentTime) {
                readyQueue.offer(task);
                arrivedTasks.add(task);
            }
        }
        
        while (!readyQueue.isEmpty() || arrivedTasks.size() < tasks.size()) {
            
            // Add newly arrived tasks to ready queue
            for (Task task : tasks) {
                if (!arrivedTasks.contains(task) && task.getArrTime() <= currentTime) {
                    readyQueue.offer(task);
                    arrivedTasks.add(task);
                }
            }
            
            if (readyQueue.isEmpty()) {
                // No tasks ready, advance time to next arrival
                int nextArrival = Integer.MAX_VALUE;
                for (Task task : tasks) {
                    if (!arrivedTasks.contains(task)) {
                        nextArrival = Math.min(nextArrival, (int) task.getArrTime());
                    }
                }
                if (nextArrival != Integer.MAX_VALUE) {
                    currentTime = nextArrival;
                }
                continue;
            }
            
            // Get next task from ready queue
            Task currentTask = readyQueue.poll();
            
            // Add dispatcher time
            currentTime += dispatcherTime;
            
            // Record execution start
            recordExecution(currentTime, currentTask.getPID());
            
            // Execute for time quantum or remaining time, whichever is smaller
            long executionTime = Math.min(timeQuantum, currentTask.getWorkRemaining());
            currentTime += executionTime;
            
            // Update task's remaining work
            currentTask.setWorkRemaining(currentTask.getWorkRemaining() - executionTime);
            
            // Check if task is completed
            if (currentTask.getWorkRemaining() <= 0) {
                currentTask.setFinishTime(currentTime);
            } else {
                // Task not completed, add back to ready queue after checking for new arrivals
                
                // First, add any newly arrived tasks
                for (Task task : tasks) {
                    if (!arrivedTasks.contains(task) && task.getArrTime() <= currentTime) {
                        readyQueue.offer(task);
                        arrivedTasks.add(task);
                    }
                }
                
                // Then add the current task back to the queue
                readyQueue.offer(currentTask);
            }
        }
        
        // Update turnaround and waiting times for all tasks
        updateTaskTimes();
        simulationCompleted = true;
    }
    
    /**
     * Outputs the detailed results for Round Robin algorithm
     * Shows execution order and individual task statistics
     */
    @Override
    public void outResults() {
        if (!simulationCompleted) {
            System.err.println("Error: Simulation must be run before outputting results.");
            return;
        }
        
        // Output algorithm name
        System.out.println("RR:");
        
        // Output execution order
        for (ExecutionEvent event : executionOrder) {
            System.out.println(event.toString());
        }
        
        System.out.println(); // Empty line
        
        // Output detailed results table
        System.out.println("Process  Turnaround Time  Waiting Time");
        
        // Sort tasks by PID for consistent output order
        List<Task> sortedTasks = new ArrayList<>(tasks);
        sortedTasks.sort((t1, t2) -> t1.getPID().compareTo(t2.getPID()));
        
        for (Task task : sortedTasks) {
            long turnaroundTime = calculateTurnaroundTime(task);
            long waitingTime = calculateWaitingTime(task);
            
            // Format output to match expected format
            System.out.printf("%-8s %-15d %-12d%n", 
                task.getPID(), turnaroundTime, waitingTime);
        }
        
        System.out.println(); // Empty line after results
    }
    
    /**
     * Outputs the summary statistics for Round Robin algorithm
     * Shows average turnaround time and average waiting time
     */
    @Override
    public void outSummary() {
        if (!simulationCompleted) {
            System.err.println("Error: Simulation must be run before outputting summary.");
            return;
        }
        
        double avgTurnaroundTime = calculateAverageTurnaroundTime();
        double avgWaitingTime = calculateAverageWaitingTime();
        
        // Format output to match expected format
        System.out.printf("RR         %-23s %-12s%n", 
            formatDecimal(avgTurnaroundTime), formatDecimal(avgWaitingTime));
    }
    
    /**
     * Gets the algorithm name
     * 
     * @return The name of this scheduling algorithm
     */
    public String getAlgorithmName() {
        return "RR";
    }
    
    /**
     * Gets the time quantum
     * 
     * @return The time quantum used by this algorithm
     */
    public int getTimeQuantum() {
        return timeQuantum;
    }
}
