import java.util.Collections;

/**
 * FCFSScheduling.java - First Come First Served scheduling algorithm implementation
 * This class implements the FCFS scheduling algorithm using the SchedulingAlgorithm framework
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class FCFSScheduling extends SchedulingAlgorithm {
    
    /**
     * Constructor for FCFS scheduling algorithm
     * 
     * @param dispatcherTime Time overhead for context switching
     * @param tasks List of tasks to be scheduled
     */
    public FCFSScheduling(int dispatcherTime, java.util.List<Task> tasks) {
        super(dispatcherTime, tasks);
    }
    
    /**
     * Runs the FCFS scheduling algorithm
     * Processes are executed in order of arrival time, with PID as tiebreaker
     */
    @Override
    public void run() {
        // Reset tasks and execution order for fresh simulation
        resetTasks();
        
        // Sort tasks by arrival time (and PID for ties) - already implemented in Task.compareTo()
        Collections.sort(tasks);
        
        int currentTime = 0;
        
        // Process each task in FCFS order
        for (Task task : tasks) {
            // Wait for task to arrive if necessary
            if (currentTime < task.getArrTime()) {
                currentTime = (int) task.getArrTime();
            }
            
            // Add dispatcher time (context switch overhead)
            currentTime += dispatcherTime;
            
            // Record when this process starts execution
            recordExecution(currentTime, task.getPID());
            
            // Execute the entire task (FCFS is non-preemptive)
            currentTime += task.getSrvTime();
            
            // Set the finish time for this task
            task.setFinishTime(currentTime);
        }
        
        // Update turnaround and waiting times for all tasks
        updateTaskTimes();
        simulationCompleted = true;
    }
    
    /**
     * Outputs the detailed results for FCFS algorithm
     * Shows execution order and individual task statistics
     */
    @Override
    public void outResults() {
        if (!simulationCompleted) {
            System.err.println("Error: Simulation must be run before outputting results.");
            return;
        }
        
        // Output algorithm name
        System.out.println("FCFS:");
        
        // Output execution order
        for (ExecutionEvent event : executionOrder) {
            System.out.println(event.toString());
        }
        
        System.out.println(); // Empty line
        
        // Output detailed results table
        System.out.println("Process  Turnaround Time  Waiting Time");
        
        // Sort tasks by PID for consistent output order
        java.util.List<Task> sortedTasks = new java.util.ArrayList<>(tasks);
        sortedTasks.sort((t1, t2) -> t1.getPID().compareTo(t2.getPID()));
        
        for (Task task : sortedTasks) {
            long turnaroundTime = calculateTurnaroundTime(task);
            long waitingTime = calculateWaitingTime(task);
            
            // Format output to match expected format (left-aligned with specific spacing)
            System.out.printf("%-8s %-15d %-12d%n", 
                task.getPID(), turnaroundTime, waitingTime);
        }
        
        System.out.println(); // Empty line after results
    }
    
    /**
     * Outputs the summary statistics for FCFS algorithm
     * Shows average turnaround time and average waiting time
     */
    @Override
    public void outSummary() {
        if (!simulationCompleted) {
            System.err.println("Error: Simulation must be run before outputting summary.");
            return;
        }
        
        double avgTurnaroundTime = calculateAverageTurnaroundTime();
        double avgWaitingTime = calculateAverageWaitingTime();
        
        // Format output to match expected format
        System.out.printf("FCFS       %-23s %-12s%n", 
            formatDecimal(avgTurnaroundTime), formatDecimal(avgWaitingTime));
    }
    
    /**
     * Gets the algorithm name
     * 
     * @return The name of this scheduling algorithm
     */
    public String getAlgorithmName() {
        return "FCFS";
    }
}
