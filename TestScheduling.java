import java.util.ArrayList;
import java.util.List;

/**
 * TestScheduling.java - Test class for the SchedulingAlgorithm framework
 * This class demonstrates the usage of the abstract SchedulingAlgorithm class
 * and tests the FCFS implementation
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TestScheduling {
    
    public static void main(String[] args) {
        System.out.println("=== Testing SchedulingAlgorithm Framework ===\n");
        
        // Test with datafile1 data
        testDatafile1();
        
        System.out.println("\n==================================================\n");

        // Test with datafile2 data
        testDatafile2();
    }
    
    /**
     * Test with datafile1 data (all processes arrive at time 0)
     */
    private static void testDatafile1() {
        System.out.println("Testing with datafile1 data:");
        System.out.println("All processes arrive at time 0\n");
        
        // Create tasks based on datafile1.txt
        List<Task> tasks = new ArrayList<>();
        tasks.add(new Task("p1", 0, 10));
        tasks.add(new Task("p2", 0, 1));
        tasks.add(new Task("p3", 0, 2));
        tasks.add(new Task("p4", 0, 1));
        tasks.add(new Task("p5", 0, 5));
        
        // Dispatcher time from datafile1 (DISP: 1)
        int dispatcherTime = 1;
        
        // Test FCFS algorithm
        FCFSScheduling fcfs = new FCFSScheduling(dispatcherTime, tasks);
        
        System.out.println("Running FCFS simulation...");
        fcfs.run();
        
        System.out.println("FCFS Results:");
        fcfs.outResults();
        
        // Test calculation methods
        System.out.println("Verification of calculations:");
        List<Task> fcfsTasks = fcfs.getTasks();
        for (Task task : fcfsTasks) {
            if (task.getPID().equals("p1")) {
                System.out.printf("p1: FinishTime=%d, TurnaroundTime=%d, WaitingTime=%d%n",
                    task.getFinishTime(),
                    fcfs.calculateTurnaroundTime(task),
                    fcfs.calculateWaitingTime(task));
                break;
            }
        }
        
        System.out.printf("Average Turnaround Time: %.2f%n", fcfs.calculateAverageTurnaroundTime());
        System.out.printf("Average Waiting Time: %.2f%n", fcfs.calculateAverageWaitingTime());
    }
    
    /**
     * Test with datafile2 data (processes arrive at different times)
     */
    private static void testDatafile2() {
        System.out.println("Testing with datafile2 data:");
        System.out.println("Processes arrive at different times\n");
        
        // Create tasks based on datafile2.txt
        List<Task> tasks = new ArrayList<>();
        tasks.add(new Task("p1", 0, 10));
        tasks.add(new Task("p2", 2, 1));
        tasks.add(new Task("p3", 6, 2));
        tasks.add(new Task("p4", 10, 1));
        tasks.add(new Task("p5", 14, 5));
        
        // Dispatcher time from datafile2 (DISP: 1)
        int dispatcherTime = 1;
        
        // Test FCFS algorithm
        FCFSScheduling fcfs = new FCFSScheduling(dispatcherTime, tasks);
        
        System.out.println("Running FCFS simulation...");
        fcfs.run();
        
        System.out.println("FCFS Results:");
        fcfs.outResults();
        
        System.out.printf("Average Turnaround Time: %.2f%n", fcfs.calculateAverageTurnaroundTime());
        System.out.printf("Average Waiting Time: %.2f%n", fcfs.calculateAverageWaitingTime());
    }
    
    /**
     * Demonstrates the abstract class features
     */
    private static void demonstrateAbstractFeatures() {
        System.out.println("=== Demonstrating Abstract Class Features ===\n");
        
        List<Task> tasks = new ArrayList<>();
        tasks.add(new Task("p1", 0, 5));
        tasks.add(new Task("p2", 2, 3));
        
        FCFSScheduling scheduler = new FCFSScheduling(1, tasks);
        
        // Show that simulation hasn't run yet
        System.out.println("Simulation completed: " + scheduler.isSimulationCompleted());
        
        // Run simulation
        scheduler.run();
        
        // Show that simulation has now completed
        System.out.println("Simulation completed: " + scheduler.isSimulationCompleted());
        
        // Show execution order
        System.out.println("Execution events:");
        for (SchedulingAlgorithm.ExecutionEvent event : scheduler.getExecutionOrder()) {
            System.out.println("  " + event);
        }
        
        // Show task details
        System.out.println("Task details after simulation:");
        for (Task task : scheduler.getTasks()) {
            System.out.printf("  %s: Finish=%d, Turnaround=%d, Waiting=%d%n",
                task.getPID(), task.getFinishTime(), 
                scheduler.calculateTurnaroundTime(task),
                scheduler.calculateWaitingTime(task));
        }
    }
}
