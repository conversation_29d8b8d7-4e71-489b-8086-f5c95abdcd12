import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;
import java.util.List;
import java.util.ArrayList;

/**
 * InputParser.java - Utility class for parsing input files
 * This class provides static methods to parse input files and extract dispatcher time and tasks
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class InputParser {
    
    /**
     * Custom exception for input parsing errors
     */
    public static class InputParseException extends Exception {
        public InputParseException(String message) {
            super(message);
        }
        
        public InputParseException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * Parses an input file and returns InputData containing dispatcher time and tasks
     * 
     * @param filePath The path to the input file
     * @return InputData object containing parsed information
     * @throws FileNotFoundException if the input file cannot be found
     * @throws InputParseException if the file format is invalid or parsing fails
     */
    public static InputData parseInputFile(String filePath) throws FileNotFoundException, InputParseException {
        File inputFile = new File(filePath);
        
        if (!inputFile.exists()) {
            throw new FileNotFoundException("Input file not found: " + filePath);
        }
        
        if (!inputFile.canRead()) {
            throw new InputParseException("Cannot read input file: " + filePath);
        }
        
        try (Scanner scanner = new Scanner(inputFile)) {
            return parseInput(scanner, filePath);
        } catch (Exception e) {
            if (e instanceof InputParseException) {
                throw e;
            }
            throw new InputParseException("Error reading file: " + filePath, e);
        }
    }
    
    /**
     * Internal method to parse the input using a Scanner
     * 
     * @param scanner Scanner for reading the input
     * @param filePath File path for error reporting
     * @return InputData object containing parsed information
     * @throws InputParseException if parsing fails
     */
    private static InputData parseInput(Scanner scanner, String filePath) throws InputParseException {
        int dispatcherTime = -1;
        List<Task> tasks = new ArrayList<>();
        
        ParserState state = ParserState.LOOKING_FOR_BEGIN;
        String currentPID = null;
        Long currentArrTime = null;
        Long currentSrvTime = null;
        int lineNumber = 0;
        
        while (scanner.hasNextLine()) {
            lineNumber++;
            String line = scanner.nextLine().trim();
            
            // Skip empty lines
            if (line.isEmpty()) {
                continue;
            }
            
            try {
                switch (state) {
                    case LOOKING_FOR_BEGIN:
                        if (line.equals("BEGIN")) {
                            state = ParserState.IN_HEADER;
                        } else {
                            throw new InputParseException("Expected 'BEGIN' at line " + lineNumber + ", found: " + line);
                        }
                        break;
                        
                    case IN_HEADER:
                        if (line.startsWith("DISP:")) {
                            dispatcherTime = parseDispatcherTime(line, lineNumber);
                        } else if (line.equals("END")) {
                            if (dispatcherTime == -1) {
                                throw new InputParseException("DISP value not found in header section");
                            }
                            state = ParserState.LOOKING_FOR_PROCESS_OR_EOF;
                        } else {
                            throw new InputParseException("Unexpected line in header at line " + lineNumber + ": " + line);
                        }
                        break;
                        
                    case LOOKING_FOR_PROCESS_OR_EOF:
                        if (line.equals("EOF")) {
                            state = ParserState.FINISHED;
                        } else if (line.startsWith("PID:")) {
                            currentPID = parsePID(line, lineNumber);
                            currentArrTime = null;
                            currentSrvTime = null;
                            state = ParserState.IN_PROCESS;
                        } else {
                            throw new InputParseException("Expected 'PID:' or 'EOF' at line " + lineNumber + ", found: " + line);
                        }
                        break;
                        
                    case IN_PROCESS:
                        if (line.startsWith("ArrTime:")) {
                            currentArrTime = parseArrTime(line, lineNumber);
                        } else if (line.startsWith("SrvTime:")) {
                            currentSrvTime = parseSrvTime(line, lineNumber);
                        } else if (line.equals("END")) {
                            // Validate that we have all required fields
                            if (currentPID == null || currentArrTime == null || currentSrvTime == null) {
                                throw new InputParseException("Incomplete process definition ending at line " + lineNumber);
                            }
                            
                            // Create and add the task
                            Task task = new Task(currentPID, currentArrTime, currentSrvTime);
                            tasks.add(task);
                            
                            // Reset for next process
                            currentPID = null;
                            currentArrTime = null;
                            currentSrvTime = null;
                            state = ParserState.LOOKING_FOR_PROCESS_OR_EOF;
                        } else {
                            throw new InputParseException("Unexpected line in process definition at line " + lineNumber + ": " + line);
                        }
                        break;
                        
                    case FINISHED:
                        throw new InputParseException("Unexpected content after EOF at line " + lineNumber + ": " + line);
                }
            } catch (NumberFormatException e) {
                throw new InputParseException("Invalid number format at line " + lineNumber + ": " + line, e);
            }
        }
        
        // Validate final state
        if (state != ParserState.FINISHED) {
            throw new InputParseException("Unexpected end of file. Expected 'EOF' marker.");
        }
        
        if (tasks.isEmpty()) {
            throw new InputParseException("No processes found in input file");
        }
        
        return new InputData(dispatcherTime, tasks);
    }
    
    /**
     * Parses the dispatcher time from a DISP: line
     */
    private static int parseDispatcherTime(String line, int lineNumber) throws InputParseException {
        String[] parts = line.split(":");
        if (parts.length != 2) {
            throw new InputParseException("Invalid DISP format at line " + lineNumber + ": " + line);
        }
        
        try {
            int dispTime = Integer.parseInt(parts[1].trim());
            if (dispTime < 0) {
                throw new InputParseException("Dispatcher time cannot be negative at line " + lineNumber + ": " + dispTime);
            }
            return dispTime;
        } catch (NumberFormatException e) {
            throw new InputParseException("Invalid dispatcher time format at line " + lineNumber + ": " + parts[1].trim(), e);
        }
    }
    
    /**
     * Parses the process ID from a PID: line
     */
    private static String parsePID(String line, int lineNumber) throws InputParseException {
        String[] parts = line.split(":");
        if (parts.length != 2) {
            throw new InputParseException("Invalid PID format at line " + lineNumber + ": " + line);
        }
        
        String pid = parts[1].trim();
        if (pid.isEmpty()) {
            throw new InputParseException("Empty PID at line " + lineNumber);
        }
        
        return pid;
    }
    
    /**
     * Parses the arrival time from an ArrTime: line
     */
    private static long parseArrTime(String line, int lineNumber) throws InputParseException {
        String[] parts = line.split(":");
        if (parts.length != 2) {
            throw new InputParseException("Invalid ArrTime format at line " + lineNumber + ": " + line);
        }
        
        try {
            long arrTime = Long.parseLong(parts[1].trim());
            if (arrTime < 0) {
                throw new InputParseException("Arrival time cannot be negative at line " + lineNumber + ": " + arrTime);
            }
            return arrTime;
        } catch (NumberFormatException e) {
            throw new InputParseException("Invalid arrival time format at line " + lineNumber + ": " + parts[1].trim(), e);
        }
    }
    
    /**
     * Parses the service time from a SrvTime: line
     */
    private static long parseSrvTime(String line, int lineNumber) throws InputParseException {
        String[] parts = line.split(":");
        if (parts.length != 2) {
            throw new InputParseException("Invalid SrvTime format at line " + lineNumber + ": " + line);
        }
        
        try {
            long srvTime = Long.parseLong(parts[1].trim());
            if (srvTime <= 0) {
                throw new InputParseException("Service time must be positive at line " + lineNumber + ": " + srvTime);
            }
            return srvTime;
        } catch (NumberFormatException e) {
            throw new InputParseException("Invalid service time format at line " + lineNumber + ": " + parts[1].trim(), e);
        }
    }
    
    /**
     * Enum to track parser state
     */
    private enum ParserState {
        LOOKING_FOR_BEGIN,
        IN_HEADER,
        LOOKING_FOR_PROCESS_OR_EOF,
        IN_PROCESS,
        FINISHED
    }
}
