import java.util.List;
import java.util.ArrayList;

/**
 * InputData.java - Data container for parsed input file information
 * This class encapsulates the dispatcher time and list of tasks from an input file
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class InputData {
    
    private final int dispatcherTime;
    private final List<Task> tasks;
    
    /**
     * Constructor for InputData
     * 
     * @param dispatcherTime The dispatcher time from the input file
     * @param tasks The list of tasks parsed from the input file
     */
    public InputData(int dispatcherTime, List<Task> tasks) {
        this.dispatcherTime = dispatcherTime;
        this.tasks = new ArrayList<>(tasks); // Create defensive copy
    }
    
    /**
     * Gets the dispatcher time
     * 
     * @return The dispatcher time
     */
    public int getDispatcherTime() {
        return dispatcherTime;
    }
    
    /**
     * Gets a copy of the tasks list
     * 
     * @return A new list containing copies of all tasks
     */
    public List<Task> getTasks() {
        return new ArrayList<>(tasks);
    }
    
    /**
     * Gets the number of tasks
     * 
     * @return The number of tasks in the input data
     */
    public int getTaskCount() {
        return tasks.size();
    }
    
    /**
     * Checks if the input data is valid
     * 
     * @return true if dispatcher time is non-negative and there are tasks, false otherwise
     */
    public boolean isValid() {
        return dispatcherTime >= 0 && !tasks.isEmpty();
    }
    
    /**
     * String representation of the input data
     * 
     * @return String representation showing dispatcher time and task count
     */
    @Override
    public String toString() {
        return String.format("InputData{dispatcherTime=%d, taskCount=%d}", 
                           dispatcherTime, tasks.size());
    }
    
    /**
     * Detailed string representation including all tasks
     * 
     * @return Detailed string representation
     */
    public String toDetailedString() {
        StringBuilder sb = new StringBuilder();
        sb.append("InputData:\n");
        sb.append("  Dispatcher Time: ").append(dispatcherTime).append("\n");
        sb.append("  Tasks (").append(tasks.size()).append("):\n");
        
        for (Task task : tasks) {
            sb.append("    ").append(task.toString()).append("\n");
        }
        
        return sb.toString();
    }
}
