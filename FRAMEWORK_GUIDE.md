# SchedulingAlgorithm Framework Guide

## Overview

The `SchedulingAlgorithm` abstract class provides a comprehensive framework for implementing process scheduling algorithms. This guide explains how to use and extend the framework.

## Framework Architecture

### Abstract Base Class: SchedulingAlgorithm

The `SchedulingAlgorithm` class provides:
- Common data structures for tasks and execution tracking
- Helper methods for time calculations
- Abstract methods that must be implemented by subclasses
- Standardized output formatting

### Key Components

1. **Task Management**: Handles task copying, resetting, and state management
2. **Execution Recording**: Tracks when processes start execution using `ExecutionEvent`
3. **Time Calculations**: Provides methods for turnaround and waiting time calculations
4. **Output Formatting**: Ensures consistent output format across all algorithms

## Creating a New Scheduling Algorithm

To implement a new scheduling algorithm, follow these steps:

### Step 1: Extend SchedulingAlgorithm

```java
public class YourAlgorithm extends SchedulingAlgorithm {
    
    public YourAlgorithm(int dispatcherTime, List<Task> tasks) {
        super(dispatcherTime, tasks);
        // Add any algorithm-specific parameters
    }
}
```

### Step 2: Implement the run() Method

```java
@Override
public void run() {
    // Reset tasks for fresh simulation
    resetTasks();
    
    // Your algorithm logic here
    int currentTime = 0;
    
    // Example: Process tasks according to your algorithm
    for (Task task : tasks) {
        // Wait for task arrival
        if (currentTime < task.getArrTime()) {
            currentTime = (int) task.getArrTime();
        }
        
        // Add dispatcher time
        currentTime += dispatcherTime;
        
        // Record execution start
        recordExecution(currentTime, task.getPID());
        
        // Execute task (modify as needed for your algorithm)
        currentTime += task.getSrvTime();
        task.setFinishTime(currentTime);
    }
    
    // Update task times and mark simulation complete
    updateTaskTimes();
    simulationCompleted = true;
}
```

### Step 3: Implement outResults() Method

```java
@Override
public void outResults() {
    if (!simulationCompleted) {
        System.err.println("Error: Simulation must be run before outputting results.");
        return;
    }
    
    // Output algorithm name
    System.out.println("YOUR_ALGORITHM_NAME:");
    
    // Output execution order
    for (ExecutionEvent event : executionOrder) {
        System.out.println(event.toString());
    }
    
    System.out.println(); // Empty line
    
    // Output results table
    System.out.println("Process  Turnaround Time  Waiting Time");
    
    // Sort tasks by PID for consistent output
    List<Task> sortedTasks = new ArrayList<>(tasks);
    sortedTasks.sort((t1, t2) -> t1.getPID().compareTo(t2.getPID()));
    
    for (Task task : sortedTasks) {
        long turnaroundTime = calculateTurnaroundTime(task);
        long waitingTime = calculateWaitingTime(task);
        
        System.out.printf("%-8s %-15d %-12d%n", 
            task.getPID(), turnaroundTime, waitingTime);
    }
    
    System.out.println(); // Empty line
}
```

### Step 4: Implement outSummary() Method

```java
@Override
public void outSummary() {
    if (!simulationCompleted) {
        System.err.println("Error: Simulation must be run before outputting summary.");
        return;
    }
    
    double avgTurnaroundTime = calculateAverageTurnaroundTime();
    double avgWaitingTime = calculateAverageWaitingTime();
    
    System.out.printf("YOUR_ALG   %-23s %-12s%n", 
        formatDecimal(avgTurnaroundTime), formatDecimal(avgWaitingTime));
}
```

## Available Helper Methods

### Time Calculations
- `calculateTurnaroundTime(Task task)` - Returns FinishTime - ArrivalTime
- `calculateWaitingTime(Task task)` - Returns TurnaroundTime - ServiceTime
- `calculateAverageTurnaroundTime()` - Returns average across all tasks
- `calculateAverageWaitingTime()` - Returns average across all tasks

### Task Management
- `resetTasks()` - Resets all tasks to initial state
- `updateTaskTimes()` - Updates turnaround and waiting times for all tasks
- `getTasks()` - Returns copy of tasks list

### Execution Recording
- `recordExecution(int time, String processId)` - Records when a process starts
- `getExecutionOrder()` - Returns list of execution events

### Utility Methods
- `formatDecimal(double value)` - Formats double to 2 decimal places
- `isSimulationCompleted()` - Checks if simulation has been run

## Example: Priority Scheduling

```java
public class PriorityScheduling extends SchedulingAlgorithm {
    
    public PriorityScheduling(int dispatcherTime, List<Task> tasks) {
        super(dispatcherTime, tasks);
    }
    
    @Override
    public void run() {
        resetTasks();
        
        // Sort by priority (assuming priority is stored in Task)
        // tasks.sort((t1, t2) -> Integer.compare(t1.getPriority(), t2.getPriority()));
        
        int currentTime = 0;
        for (Task task : tasks) {
            if (currentTime < task.getArrTime()) {
                currentTime = (int) task.getArrTime();
            }
            
            currentTime += dispatcherTime;
            recordExecution(currentTime, task.getPID());
            
            currentTime += task.getSrvTime();
            task.setFinishTime(currentTime);
        }
        
        updateTaskTimes();
        simulationCompleted = true;
    }
    
    // Implement outResults() and outSummary() as shown above
}
```

## Best Practices

1. **Always call `resetTasks()`** at the beginning of `run()`
2. **Always call `updateTaskTimes()`** at the end of `run()`
3. **Set `simulationCompleted = true`** after successful simulation
4. **Check `simulationCompleted`** in output methods
5. **Use `recordExecution()`** to track process starts
6. **Sort tasks by PID** in output methods for consistency
7. **Handle edge cases** like no tasks or empty ready queues

## Testing Your Algorithm

```java
public static void main(String[] args) {
    List<Task> tasks = new ArrayList<>();
    tasks.add(new Task("p1", 0, 10));
    tasks.add(new Task("p2", 0, 1));
    
    YourAlgorithm algorithm = new YourAlgorithm(1, tasks);
    algorithm.run();
    algorithm.outResults();
    algorithm.outSummary();
}
```

This framework ensures consistent behavior and output formatting across all scheduling algorithms while providing the flexibility to implement any scheduling policy.
