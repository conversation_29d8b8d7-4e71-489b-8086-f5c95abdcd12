# Assignment 1 - Process Scheduling Simulation

## Project Structure

This Java project implements a process scheduling simulation with the following files:

### Core Classes

- **A1.java** - Main entry point for the program
  - <PERSON>les command-line arguments (expects input file name)
  - Demonstrates file reading using `java.util.Scanner`
  - Includes error handling for file operations
  - Ready for extension with scheduling algorithms

- **Task.java** - Represents a process in the scheduling simulation
  - Implements `Comparable<Task>` interface for sorting
  - Contains all required properties for process scheduling
  - Includes methods for execution simulation and state management

### Test Files

- **TestTask.java** - Unit tests for the Task class
  - Validates Task creation, execution, and reset functionality
  - Tests the Comparable implementation (sorting by arrival time and PID)
  - Demonstrates proper usage of all Task methods

## Task Class Properties

The Task class encapsulates the following properties:

- `PID` (String) - Process ID (e.g., "p1")
- `ArrTime` (long) - Arrival time
- `SrvTime` (long) - Service time
- `WorkRemaining` (long) - Remaining service time
- `FinishTime` (long) - When the process completes
- `WaitingTime` (long) - Total waiting time
- `TurnaroundTime` (long) - Total turnaround time
- `LastRunTime` (long) - Last time the process was run
- `CurrentQuantum` (long) - Time quantum for SRR and FB algorithms

## Key Features

### Task Class Methods

- `reset()` - Resets task to initial state for new simulation runs
- `execute(long timeSlice)` - Simulates task execution for given time
- `isCompleted()` - Checks if task has finished execution
- Standard getters and setters for all properties
- `compareTo()` - Sorts by arrival time, then by PID numerically

### Sorting Behavior

Tasks are sorted primarily by arrival time. If two tasks have the same arrival time, they are sorted by PID numerically (p1 before p2, etc.).

## Compilation and Usage

### Compilation
```bash
javac *.java
```

For Java 8 compatibility:
```bash
javac -source 8 -target 8 *.java
```

### Running the Main Program
```bash
java A1 <input_file>
```

Example:
```bash
java A1 datafile1.txt
```

### Running Tests
```bash
java TestTask
```

## Input File Format

The program expects input files in the following format:
```
BEGIN

DISP: 1
END

PID: p1
ArrTime: 0
SrvTime: 10
END

PID: p2
ArrTime: 0
SrvTime: 1
END

...

EOF
```

## Error Handling

The program includes comprehensive error handling:
- Validates command-line arguments
- Handles file not found exceptions
- Provides clear error messages
- Graceful exit with appropriate error codes

## Next Steps

This foundation is ready for implementing the required scheduling algorithms:
- FCFS (First Come First Served)
- SPN (Shortest Process Next)
- PP (Preemptive Priority)
- PRR (Preemptive Round Robin)
- SRR (Selfish Round Robin)
- FB (Feedback)

## Compatibility

- Compatible with Java 8 and later versions
- Follows Java coding best practices
- Includes proper documentation and comments
