import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * TestTask.java - Simple test class to verify Task functionality
 * This class demonstrates the Task class features and validates the implementation
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TestTask {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Task Class ===\n");
        
        // Test 1: Basic Task creation and getters
        System.out.println("Test 1: Basic Task Creation");
        Task task1 = new Task("p1", 0, 10);
        System.out.println("Created: " + task1);
        System.out.println("PID: " + task1.getPID());
        System.out.println("Arrival Time: " + task1.getArrTime());
        System.out.println("Service Time: " + task1.getSrvTime());
        System.out.println("Work Remaining: " + task1.getWorkRemaining());
        System.out.println("Is Completed: " + task1.isCompleted());
        System.out.println();
        
        // Test 2: Task execution
        System.out.println("Test 2: Task Execution");
        System.out.println("Executing task for 3 time units...");
        long executed = task1.execute(3);
        System.out.println("Time executed: " + executed);
        System.out.println("Work remaining: " + task1.getWorkRemaining());
        System.out.println("Is completed: " + task1.isCompleted());
        System.out.println();
        
        // Test 3: Complete task execution
        System.out.println("Test 3: Complete Task Execution");
        System.out.println("Executing remaining work...");
        executed = task1.execute(10); // Try to execute more than remaining
        System.out.println("Time executed: " + executed);
        System.out.println("Work remaining: " + task1.getWorkRemaining());
        System.out.println("Is completed: " + task1.isCompleted());
        System.out.println();
        
        // Test 4: Reset functionality
        System.out.println("Test 4: Reset Functionality");
        task1.setFinishTime(15);
        task1.setWaitingTime(5);
        task1.setTurnaroundTime(15);
        System.out.println("Before reset: " + task1);
        task1.reset();
        System.out.println("After reset: " + task1);
        System.out.println();
        
        // Test 5: Comparable implementation (sorting)
        System.out.println("Test 5: Sorting Tasks");
        List<Task> tasks = new ArrayList<>();
        tasks.add(new Task("p3", 5, 2));
        tasks.add(new Task("p1", 0, 10));
        tasks.add(new Task("p2", 0, 1));
        tasks.add(new Task("p4", 5, 1));
        
        System.out.println("Before sorting:");
        for (Task task : tasks) {
            System.out.println("  " + task.getPID() + " (ArrTime: " + task.getArrTime() + ")");
        }
        
        Collections.sort(tasks);
        
        System.out.println("After sorting:");
        for (Task task : tasks) {
            System.out.println("  " + task.getPID() + " (ArrTime: " + task.getArrTime() + ")");
        }
        System.out.println();
        
        // Test 6: Setters
        System.out.println("Test 6: Setter Methods");
        Task task2 = new Task("p5", 10, 5);
        task2.setFinishTime(20);
        task2.setWaitingTime(5);
        task2.setTurnaroundTime(10);
        task2.setLastRunTime(15);
        task2.setCurrentQuantum(4);
        
        System.out.println("Task with set values:");
        System.out.println("  Finish Time: " + task2.getFinishTime());
        System.out.println("  Waiting Time: " + task2.getWaitingTime());
        System.out.println("  Turnaround Time: " + task2.getTurnaroundTime());
        System.out.println("  Last Run Time: " + task2.getLastRunTime());
        System.out.println("  Current Quantum: " + task2.getCurrentQuantum());
        
        System.out.println("\n=== All Tests Completed ===");
    }
}
