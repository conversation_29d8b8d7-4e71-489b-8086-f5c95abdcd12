import java.io.FileNotFoundException;

/**
 * InputParserExample.java - Comprehensive example of using the InputParser utility
 * This class demonstrates how to use InputParser and InputData in various scenarios
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class InputParserExample {
    
    public static void main(String[] args) {
        System.out.println("=== InputParser Usage Examples ===\n");
        
        // Example 1: Basic usage
        demonstrateBasicUsage();
        
        System.out.println("\n============================================================\n");

        // Example 2: Using parsed data with scheduling algorithms
        demonstrateSchedulingIntegration();

        System.out.println("\n============================================================\n");
        
        // Example 3: Error handling
        demonstrateErrorHandling();
    }
    
    /**
     * Demonstrates basic InputParser usage
     */
    private static void demonstrateBasicUsage() {
        System.out.println("=== Basic InputParser Usage ===");
        
        try {
            // Parse input file
            InputData inputData = InputParser.parseInputFile("datafile1.txt");
            
            // Access parsed information
            System.out.println("File parsed successfully!");
            System.out.println("Dispatcher Time: " + inputData.getDispatcherTime());
            System.out.println("Number of Tasks: " + inputData.getTaskCount());
            System.out.println("Data is valid: " + inputData.isValid());
            
            // Display tasks
            System.out.println("\nParsed Tasks:");
            for (Task task : inputData.getTasks()) {
                System.out.printf("  %s: arrives at %d, needs %d time units%n",
                    task.getPID(), task.getArrTime(), task.getSrvTime());
            }
            
            // Show detailed representation
            System.out.println("\nDetailed Information:");
            System.out.println(inputData.toString());
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates integration with scheduling algorithms
     */
    private static void demonstrateSchedulingIntegration() {
        System.out.println("=== Scheduling Algorithm Integration ===");
        
        try {
            // Parse input
            InputData inputData = InputParser.parseInputFile("datafile2.txt");
            System.out.println("Parsed " + inputData.getTaskCount() + " tasks from datafile2.txt");
            
            // Create and run FCFS algorithm
            FCFSScheduling fcfs = new FCFSScheduling(
                inputData.getDispatcherTime(), 
                inputData.getTasks()
            );
            
            System.out.println("\nRunning FCFS algorithm...");
            fcfs.run();
            
            // Display results
            System.out.println("FCFS Results:");
            fcfs.outResults();
            
            // Create and run Round Robin algorithm
            RoundRobinScheduling rr = new RoundRobinScheduling(
                inputData.getDispatcherTime(), 
                inputData.getTasks(), 
                4  // time quantum
            );
            
            System.out.println("Running Round Robin algorithm (quantum=4)...");
            rr.run();
            
            System.out.println("Round Robin Results:");
            rr.outResults();
            
            // Compare algorithms
            System.out.println("Algorithm Comparison:");
            System.out.println("Algorithm  Average Turnaround Time  Waiting Time");
            fcfs.outSummary();
            rr.outSummary();
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates error handling capabilities
     */
    private static void demonstrateErrorHandling() {
        System.out.println("=== Error Handling Examples ===");
        
        // Test 1: File not found
        System.out.println("1. Testing file not found:");
        try {
            InputParser.parseInputFile("nonexistent_file.txt");
            System.err.println("   ERROR: Should have thrown exception!");
        } catch (FileNotFoundException e) {
            System.out.println("   ✓ Correctly caught: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("   ✗ Unexpected exception: " + e.getMessage());
        }
        
        // Test 2: Valid vs Invalid InputData
        System.out.println("\n2. Testing InputData validation:");
        try {
            // Valid data
            java.util.List<Task> validTasks = new java.util.ArrayList<>();
            validTasks.add(new Task("p1", 0, 5));
            validTasks.add(new Task("p2", 2, 3));
            InputData validData = new InputData(1, validTasks);
            System.out.println("   Valid data check: " + validData.isValid());
            
            // Invalid data (empty task list)
            java.util.List<Task> emptyTasks = new java.util.ArrayList<>();
            InputData invalidData = new InputData(1, emptyTasks);
            System.out.println("   Empty task list check: " + !invalidData.isValid());
            
            // Invalid data (negative dispatcher time)
            InputData negativeDispData = new InputData(-1, validTasks);
            System.out.println("   Negative dispatcher time check: " + !negativeDispData.isValid());
            
        } catch (Exception e) {
            System.err.println("   ✗ Validation test failed: " + e.getMessage());
        }
        
        // Test 3: Defensive copying
        System.out.println("\n3. Testing defensive copying:");
        try {
            InputData inputData = InputParser.parseInputFile("datafile1.txt");
            
            // Get tasks and try to modify
            java.util.List<Task> tasks = inputData.getTasks();
            int originalSize = tasks.size();
            
            // Try to modify the returned list
            tasks.clear();
            
            // Check that original data is unchanged
            java.util.List<Task> tasksAgain = inputData.getTasks();
            System.out.println("   Original size: " + originalSize);
            System.out.println("   Size after clear: " + tasksAgain.size());
            System.out.println("   ✓ Defensive copying works: " + (originalSize == tasksAgain.size()));
            
        } catch (Exception e) {
            System.err.println("   ✗ Defensive copying test failed: " + e.getMessage());
        }
    }
    
    /**
     * Helper method to create separator (Java 8 compatible)
     */
    private static String repeat(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
