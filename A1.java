import java.io.File;
import java.io.FileNotFoundException;
import java.util.Scanner;
import java.util.List;
import java.util.ArrayList;

/**
 * A1.java - Main entry point for the assignment
 * This class handles command-line arguments and file input processing
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class A1 {
    
    /**
     * Main method that accepts a single command-line argument for input file name
     * 
     * @param args Command line arguments - expects exactly one argument (input file name)
     */
    public static void main(String[] args) {
        // Check if exactly one argument is provided
        if (args.length != 1) {
            System.err.println("Usage: java A1 <input_file>");
            System.err.println("Please provide exactly one input file name as argument.");
            System.exit(1);
        }
        
        String inputFileName = args[0];
        
        try {
            // Demonstrate file reading using Scanner
            processInputFile(inputFileName);
            
        } catch (FileNotFoundException e) {
            System.err.println("Error: Input file '" + inputFileName + "' not found.");
            System.err.println("Please ensure the file exists and the path is correct.");
            System.exit(1);
        } catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * Processes the input file using Scanner to read and parse the content
     * 
     * @param fileName The name of the input file to process
     * @throws FileNotFoundException if the specified file cannot be found
     */
    private static void processInputFile(String fileName) throws FileNotFoundException {
        File inputFile = new File(fileName);
        
        try (Scanner scanner = new Scanner(inputFile)) {
            System.out.println("Successfully opened file: " + fileName);
            
            // Read and process the file content
            // This is a demonstration - actual implementation will depend on file format
            int lineNumber = 1;
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                
                // Skip empty lines
                if (line.isEmpty()) {
                    lineNumber++;
                    continue;
                }
                
                System.out.println("Line " + lineNumber + ": " + line);
                
                // TODO: Parse the line according to assignment requirements
                // This will likely involve creating Task objects from the input data
                
                lineNumber++;
            }
            
            System.out.println("File processing completed successfully.");
            
        } catch (FileNotFoundException e) {
            throw e; // Re-throw to be handled by caller
        }
    }
    
    // TODO: Add methods for scheduling algorithms (FCFS, SPN, PP, PRR, SRR, FB)
    // TODO: Add method to parse tasks from input file
    // TODO: Add methods to run simulations and generate output
}
