import java.io.FileNotFoundException;

/**
 * A1.java - Main entry point for the assignment
 * This class handles command-line arguments and file input processing
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class A1 {
    
    /**
     * Main method that accepts a single command-line argument for input file name
     * 
     * @param args Command line arguments - expects exactly one argument (input file name)
     */
    public static void main(String[] args) {
        // Check if exactly one argument is provided
        if (args.length != 1) {
            System.err.println("Usage: java A1 <input_file>");
            System.err.println("Please provide exactly one input file name as argument.");
            System.exit(1);
        }
        
        String inputFileName = args[0];
        
        try {
            // Parse input file using InputParser
            InputData inputData = InputParser.parseInputFile(inputFileName);

            // Process the parsed data
            processInputData(inputData);

        } catch (FileNotFoundException e) {
            System.err.println("Error: Input file '" + inputFileName + "' not found.");
            System.err.println("Please ensure the file exists and the path is correct.");
            System.exit(1);
        } catch (InputParser.InputParseException e) {
            System.err.println("Error parsing input file: " + e.getMessage());
            System.exit(1);
        } catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * Processes the parsed input data and runs scheduling simulations
     *
     * @param inputData The parsed input data containing dispatcher time and tasks
     */
    private static void processInputData(InputData inputData) {
        System.out.println("Successfully parsed input file");
        System.out.println("Dispatcher Time: " + inputData.getDispatcherTime());
        System.out.println("Number of Tasks: " + inputData.getTaskCount());

        // Display parsed tasks
        System.out.println("\nParsed Tasks:");
        for (Task task : inputData.getTasks()) {
            System.out.printf("  %s: ArrTime=%d, SrvTime=%d%n",
                task.getPID(), task.getArrTime(), task.getSrvTime());
        }

        // Run scheduling algorithms
        runSchedulingAlgorithms(inputData);
    }

    /**
     * Runs all scheduling algorithms with the parsed input data
     *
     * @param inputData The parsed input data
     */
    private static void runSchedulingAlgorithms(InputData inputData) {
        System.out.println("\n============================================================");
        System.out.println("Running Scheduling Algorithms");
        System.out.println("============================================================");

        // Run FCFS
        System.out.println("\n--- FCFS Algorithm ---");
        FCFSScheduling fcfs = new FCFSScheduling(inputData.getDispatcherTime(), inputData.getTasks());
        fcfs.run();
        fcfs.outResults();

        // Run Round Robin (with quantum 4 as example)
        System.out.println("--- Round Robin Algorithm (Quantum=4) ---");
        RoundRobinScheduling rr = new RoundRobinScheduling(inputData.getDispatcherTime(), inputData.getTasks(), 4);
        rr.run();
        rr.outResults();

        // Summary comparison
        System.out.println("Summary");
        System.out.println("Algorithm  Average Turnaround Time  Waiting Time");
        fcfs.outSummary();
        rr.outSummary();
    }

}
