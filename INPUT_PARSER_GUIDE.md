# InputParser Utility Guide

## Overview

The `InputParser` utility class provides a robust, static method for parsing structured input files and converting them into `InputData` objects. This guide explains how to use the parser and handle various scenarios.

## Quick Start

### Basic Usage

```java
import java.io.FileNotFoundException;

try {
    // Parse input file
    InputData inputData = InputParser.parseInputFile("datafile1.txt");
    
    // Access parsed information
    int dispatcherTime = inputData.getDispatcherTime();
    List<Task> tasks = inputData.getTasks();
    
    // Use with scheduling algorithms
    FCFSScheduling fcfs = new FCFSScheduling(dispatcherTime, tasks);
    fcfs.run();
    fcfs.outResults();
    
} catch (FileNotFoundException e) {
    System.err.println("File not found: " + e.getMessage());
} catch (InputParser.InputParseException e) {
    System.err.println("Parse error: " + e.getMessage());
}
```

## InputData Class

### Properties
- `dispatcherTime` (int) - Time overhead for context switching
- `tasks` (List<Task>) - List of parsed tasks

### Methods
- `getDispatcherTime()` - Returns dispatcher time
- `getTasks()` - Returns defensive copy of tasks list
- `getTaskCount()` - Returns number of tasks
- `isValid()` - Validates data (non-negative dispatcher time, non-empty task list)
- `toString()` - Basic string representation
- `toDetailedString()` - Detailed string representation with all tasks

## Input File Format

The parser expects files in this exact format:

```
BEGIN
DISP: <dispatcher_time>
END

PID: <process_id>
ArrTime: <arrival_time>
SrvTime: <service_time>
END

PID: <process_id>
ArrTime: <arrival_time>
SrvTime: <service_time>
END

...

EOF
```

### Format Rules

1. **File Structure**:
   - Must start with `BEGIN`
   - Header section contains `DISP: <value>` and ends with `END`
   - Each process has `PID:`, `ArrTime:`, `SrvTime:` and ends with `END`
   - File must end with `EOF`

2. **Data Types**:
   - Dispatcher time: non-negative integer
   - Process ID: non-empty string
   - Arrival time: non-negative long
   - Service time: positive long

3. **Whitespace**: Empty lines are ignored, leading/trailing whitespace is trimmed

## Error Handling

### Exception Types

1. **FileNotFoundException**: File doesn't exist or can't be read
2. **InputParseException**: Malformed input or invalid data

### Common Parse Errors

- Missing `BEGIN` or `EOF` markers
- Invalid number formats
- Negative arrival times or non-positive service times
- Incomplete process definitions
- Unexpected content after `EOF`

### Error Messages

The parser provides detailed error messages with line numbers:

```
Invalid service time format at line 7: invalid_number
Expected 'PID:' or 'EOF' at line 15, found: INVALID
Incomplete process definition ending at line 12
```

## Advanced Features

### State Machine Parsing

The parser uses a finite state machine with these states:
- `LOOKING_FOR_BEGIN` - Expecting file to start with BEGIN
- `IN_HEADER` - Processing dispatcher time section
- `LOOKING_FOR_PROCESS_OR_EOF` - Expecting new process or end of file
- `IN_PROCESS` - Processing process definition
- `FINISHED` - File parsing complete

### Data Validation

- **Dispatcher Time**: Must be non-negative
- **Process ID**: Must be non-empty string
- **Arrival Time**: Must be non-negative
- **Service Time**: Must be positive
- **File Structure**: Must follow exact format

### Defensive Programming

- **Immutable Results**: InputData provides defensive copies
- **Input Validation**: All data is validated during parsing
- **Resource Management**: Uses try-with-resources for file handling
- **Clear Error Messages**: Specific error descriptions with line numbers

## Integration Examples

### With Scheduling Algorithms

```java
// Parse input
InputData inputData = InputParser.parseInputFile("input.txt");

// Run multiple algorithms
FCFSScheduling fcfs = new FCFSScheduling(
    inputData.getDispatcherTime(), 
    inputData.getTasks()
);

RoundRobinScheduling rr = new RoundRobinScheduling(
    inputData.getDispatcherTime(), 
    inputData.getTasks(), 
    4  // time quantum
);

fcfs.run();
rr.run();

// Compare results
fcfs.outSummary();
rr.outSummary();
```

### Error Handling Pattern

```java
public static void processFile(String filename) {
    try {
        InputData inputData = InputParser.parseInputFile(filename);
        
        if (!inputData.isValid()) {
            System.err.println("Invalid input data");
            return;
        }
        
        // Process the data...
        
    } catch (FileNotFoundException e) {
        System.err.println("File not found: " + filename);
    } catch (InputParser.InputParseException e) {
        System.err.println("Parse error: " + e.getMessage());
    } catch (Exception e) {
        System.err.println("Unexpected error: " + e.getMessage());
    }
}
```

## Testing

The parser includes comprehensive test coverage:

- **Valid Input Files**: Tests with datafile1.txt and datafile2.txt
- **Error Conditions**: File not found, malformed input, invalid data
- **Edge Cases**: Empty files, missing sections, invalid numbers
- **Integration**: Works with scheduling algorithm framework
- **Defensive Copying**: Ensures data immutability

## Best Practices

1. **Always handle exceptions** when calling `parseInputFile()`
2. **Validate InputData** using `isValid()` method
3. **Use defensive copies** - don't modify returned task lists
4. **Provide clear error messages** to users
5. **Test with various input files** including edge cases

## Performance

- **Memory Efficient**: Creates minimal object copies
- **Fast Parsing**: Single-pass state machine parser
- **Resource Safe**: Automatic file handle cleanup
- **Scalable**: Handles large numbers of tasks efficiently

The InputParser utility provides a robust, well-tested foundation for parsing assignment input files with comprehensive error handling and validation.
